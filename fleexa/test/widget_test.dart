// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:fleexa/onboarding_screen.dart';

void main() {
  testWidgets('Onboarding screen shows correctly', (WidgetTester tester) async {
    // Build our onboarding screen directly
    await tester.pumpWidget(const MaterialApp(home: OnboardingScreen()));

    // Verify that our onboarding screen shows the correct content.
    expect(
      find.text('Various Collections Of The Latest Products'),
      findsOneWidget,
    );
    expect(find.text('Create Account'), findsOneWidget);
    expect(find.text('Already Have an Account'), findsOneWidget);
  });
}
